"use client";

import React, { useState } from "react";
import { FaTimes, FaEnvelope, FaSpinner } from "react-icons/fa";
import { InvoiceData } from "../types/invoice";

interface CheckoutModalProps {
  isOpen: boolean;
  onClose: () => void;
  invoice: InvoiceData;
  mode?: "checkout" | "download"; // New prop to differentiate between modes
}

export default function CheckoutModal({
  isOpen,
  onClose,
  invoice,
  mode = "checkout",
}: CheckoutModalProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleZohoRedirect = () => {
    setIsSubmitting(true);

    // Create URL with pre-filled invoice data
    const zohoBaseUrl =
      "https://forms.zohopublic.com/taxlegit21/form/quotationformtaxlegit/formperma/EODZTjWYbJ98Fp2ONUSP35ewCftNgkUvYsdll5PxLH8";

    // Add invoice details as URL parameters (if Zoho form supports pre-filling)
    const params = new URLSearchParams({
      Company_Type: invoice.companyType,
      State: invoice.state.name,
      Total_Amount: invoice.total.toString(),
      Add_Ons: invoice.addOns.map((addon) => addon.name).join(", "),
      Has_Special_Offer: invoice.hasSpecialOffer ? "Yes" : "No",
    });

    const finalUrl = `${zohoBaseUrl}?${params.toString()}`;

    // Open Zoho form in new tab
    window.open(finalUrl, "_blank");

    // Close modal and reset loading state
    setTimeout(() => {
      setIsSubmitting(false);
      onClose();
    }, 1000);
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/20 backdrop-blur-sm flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-2xl shadow-2xl max-w-md w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <h2 className="text-2xl font-bold text-gray-800">
            {mode === "download" ? "Download Quotation" : "Checkout Invoice"}
          </h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 transition-colors"
          >
            <FaTimes className="text-xl" />
          </button>
        </div>

        {/* Content */}
        <div className="p-6">
          <div className="text-center space-y-6">
            {/* Invoice Summary */}
            <div className="bg-gray-50 rounded-lg p-4">
              <h3 className="font-semibold text-gray-800 mb-3">
                Your Quotation Details
              </h3>
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span className="text-gray-600">Company Type:</span>
                  <span className="font-medium">{invoice.companyType}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">State:</span>
                  <span className="font-medium">{invoice.state.name}</span>
                </div>
                {invoice.addOns.length > 0 && (
                  <div className="flex justify-between">
                    <span className="text-gray-600">Add-ons:</span>
                    <span className="font-medium">
                      {invoice.addOns.map((addon) => addon.name).join(", ")}
                    </span>
                  </div>
                )}
                <div className="flex justify-between border-t pt-2">
                  <span className="text-gray-800 font-semibold">
                    Total Amount:
                  </span>
                  <span className="font-bold text-green-600">
                    ₹{invoice.total.toLocaleString("en-IN")}
                  </span>
                </div>
              </div>
            </div>

            {/* Redirect Message */}
            <div className="space-y-4">
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                <p className="text-blue-800 font-medium">
                  🚀 You&apos;ll be redirected to our secure quotation form
                </p>
                <p className="text-blue-600 text-sm mt-1">
                  Your details will be pre-filled for faster processing
                </p>
              </div>

              <button
                onClick={handleZohoRedirect}
                disabled={isSubmitting}
                className="w-full bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 disabled:from-gray-400 disabled:to-gray-500 text-white font-bold py-3 px-6 rounded-xl transition-all duration-200 flex items-center justify-center gap-2"
              >
                {isSubmitting ? (
                  <>
                    <FaSpinner className="animate-spin" />
                    <span>Redirecting...</span>
                  </>
                ) : (
                  <>
                    <FaEnvelope />
                    <span>Get Your Quotation</span>
                  </>
                )}
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
